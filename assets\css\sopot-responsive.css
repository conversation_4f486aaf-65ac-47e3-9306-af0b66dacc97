/* Medium screen  */
@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .up-comming-events__inner {
        padding-top: 26px;
    }

    .up-comming-events__left {
        text-align: center;
        margin-bottom: 27px;
    }

    .up-comming-events__details {
        justify-content: center;
    }

    .up-comming-events__right {
        float: none;
        text-align: center;
    }

    .feature-one__single {
        padding: 42px 20px 38px;
    }

    .feature-one__title {
        font-size: 21px;
    }

    .about-one__left {
        max-width: 600px;
        margin: 0 auto;
    }

    .about-one__right {
        max-width: 600px;
        margin: 150px auto 0;
    }

    .causes-one__content {
        padding: 30px 20px 0px;
    }

    .causes-one__list {
        padding: 0 20px 0;
    }

    .support-us__content-box {
        padding: 53px 30px 60px;
    }

    .support-us__right-content-title {
        font-size: 64px;
    }

    .skill-one__left {
        width: 45%;
    }

    .skill-one__right {
        width: 55%;
    }

    .skill-one__content {
        margin-left: 40px;
    }

    .blog-one__content {
        padding: 35px 20px 38px;
    }

    .blog-one__title {
        font-size: 22px;
    }

    .footer-widget-one__twitter-feed {
        margin-top: 28px;
    }

    .footer-widget-one__latest-works {
        margin-top: 28px;
    }

    .about-two__left {
        max-width: 600px;
        margin: 0 auto;
    }

    .about-two__right {
        max-width: 600px;
        margin: 250px auto 0;
    }

    .recent-event__single {
        padding-left: 0;
    }

    .recent-event__img {
        position: relative;
        max-width: 100%;
    }

    .faq-one__img {
        left: -15px;
    }

    .footer-widget__links {
        margin-top: 50px;
    }

    .footer-widget__map {
        margin-top: 50px;
    }

    .help-the-causes__left {
        width: 50%;
    }

    .help-the-causes__right {
        width: 50%;
    }

    .help-the-causes__content-box {
        margin-left: 0;
        padding-left: 40px;
        padding-right: 20px;
    }

    .blog-three__content {
        padding: 30px 20px 35px;
    }

    .blog-three__title {
        font-size: 22px;
    }

    .contact-page .section-title__title br {
        display: none;
    }

    .contact-info__single {
        padding: 43px 25px 43px;
    }

    .contact-info__single-3 {
        padding: 43px 60px 73px;
    }

    .project-details__list li .right {
        margin-left: 50px;
    }


    .donations-list__content {
        padding: 30px 30px 23px;
    }

    .donations-list__progress {
        padding-top: 25px;
    }

    .donations-list__title {
        margin-top: 15px;
        margin-bottom: 10px;
    }

    .site-footer-one__top {
        padding: 115px 0 120px;
    }

    .site-footer__top {
        padding-bottom: 116px;
    }

    .team-details__top-right {
        margin-left: 0;
    }

    .join-team {
        padding: 109px 0 120px;
    }

    .causes-one__title {
        font-size: 20px;
        line-height: 30px;
    }



}





/* Tablet Layout: 768px. */
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .up-comming-events__inner {
        padding-top: 26px;
    }

    .up-comming-events__left {
        text-align: center;
        margin-bottom: 27px;
    }

    .up-comming-events__details {
        justify-content: center;
    }

    .up-comming-events__right {
        float: none;
        text-align: center;
    }

    .about-one__left {
        max-width: 600px;
        margin: 0 auto;
    }

    .about-one__right {
        max-width: 600px;
        margin: 150px auto 0;
    }

    .support-us__right {
        margin-top: 26px;
    }

    .support-us {
        padding: 120px 0 112px;
    }

    .skill-one__left {
        width: 100%;
    }

    .skill-one__right {
        width: 100%;
    }

    .footer-widget-one__twitter-feed {
        margin-top: 28px;
    }

    .footer-widget-one__latest-works {
        margin-top: 28px;
    }

    .two-section__title {
        font-size: 35px;
        line-height: 45px;
    }

    .about-two__left {
        max-width: 600px;
        margin: 0 auto;
    }

    .about-two__right {
        max-width: 600px;
        margin: 250px auto 0;
    }

    .recent-event__single {
        padding-left: 0;
    }

    .recent-event__img {
        position: relative;
        max-width: 100%;
    }

    .faq-one__img {
        position: relative;
        top: 120px;
        left: 0;
    }

    .faq-one__img img {
        width: 100%;
    }

    .footer-widget__links {
        margin-top: 50px;
    }

    .footer-widget__map {
        margin-top: 50px;
    }

    .help-the-causes__left {
        width: 45%;
    }

    .help-the-causes__right {
        width: 55%;
    }

    .help-the-causes__content-box {
        margin-left: 0;
        padding-left: 35px;
        padding-right: 20px;
    }

    .up-comming-event-two__list li {
        padding: 24px 25px;
    }

    .cta-one__inner {
        justify-content: center;
        flex-direction: column;
    }

    .cta-one__right {
        margin-top: 27px;
    }

    .error-page__img img {
        width: 100%;
    }

    .blog-page .blog-one__content {
        padding: 35px 28px 38px;
    }

    .contact-page__right {
        margin-top: 50px;
    }

    .project-details__right {
        margin-top: 50px;
    }

    .join-team__Left {
        margin-bottom: 42px;
    }

    .donation-details__left {
        margin-bottom: 50px;
    }

    .donate-now__right {
        margin-top: 50px;
    }

    .site-footer-one__top {
        padding: 115px 0 146px;
    }

    .site-footer__top {
        padding-bottom: 116px;
    }

    .blog-three__content {
        padding: 30px 29px 35px;
    }

    .team-details__top-right {
        margin-left: 0;
        margin-top: 60px;
    }

    .join-team {
        padding: 109px 0 120px;
    }

    .donations-page .causes-one__title {
        font-size: 24px;
    }





}






/* Mobile Layout: 320px. */
@media only screen and (max-width: 767px) {
    .section-title__title br {
        display: none;
    }

    .up-comming-events__inner {
        padding: 26px 20px 50px;
    }

    .up-comming-events__details {
        flex-direction: column;
        align-items: baseline;
    }

    .up-comming-events__left {
        margin-bottom: 27px;
    }

    .up-comming-events__details li+li {
        margin-left: 0;
    }

    .up-comming-events__right {
        float: none;
    }

    .countdown-timer li {
        display: block;
        margin: 0px 0px 20px;
    }

    .countdown-timer li:last-child {
        margin-bottom: 0;
    }

    .countdown-timer li span.days,
    .countdown-timer li span.hours,
    .countdown-timer li span.minutes,
    .countdown-timer li span.seconds {
        margin: 0 auto 0;
    }

    .feature-one__single {
        padding: 42px 20px 38px;
    }

    .about-one__left {
        margin-right: 0;
    }

    .about-one__small-img {
        display: none;
    }

    .about-one__right {
        margin-top: 148px;
    }

    .about-one__right-content {
        padding: 75px 25px 63px;
    }

    .about-one__bottom-video-box {
        flex-direction: column;
        align-items: baseline;
    }

    .about-one__video-link {
        margin-top: 60px;
    }

    .causes-one__content {
        padding: 30px 20px 0px;
    }

    .causes-one__list {
        padding: 0 20px 0;
    }

    .support-us__content-box {
        padding: 53px 20px 60px;
    }

    .support-us__donate-form-select-box {
        flex-direction: column;
    }

    .support-us__donate-form-select-box li+li {
        margin-top: 5px;
        margin-left: 0;
    }

    .support-us__right {
        margin-top: 31px;
    }

    .support-us__right-content-title {
        font-size: 50px;
        line-height: 75px;
    }

    .support-us {
        padding: 120px 0 112px;
    }

    .skill-one__left {
        width: 100%;
    }

    .skill-one__right {
        width: 100%;
    }

    .skill-one__content {
        margin-left: 0;
        padding-right: 30px;
        padding-left: 30px;
    }

    .video-one__title br {
        display: none;
    }

    .blog-one__content {
        padding: 35px 20px 38px;
    }

    .blog-one__title {
        font-size: 22px;
    }

    .footer-widget-one__gallery {
        margin-top: 43px;
        margin-bottom: 28px;
    }

    .footer-widget-one__latest-works {
        margin-bottom: 43px;
    }

    .two-section__left {
        width: 100%;
    }

    .two-section__right {
        width: 100%;
    }

    .about-two__left {
        margin-right: 0;
    }

    .about-two__bg {
        display: none;
    }

    .about-two__right {
        margin-right: 0;
        margin-top: 130px;
    }

    .about-two-shape-1 {
        display: none;
    }

    .recent-event__single {
        padding-left: 0;
    }

    .recent-event__img {
        position: relative;
        max-width: 100%;
    }

    .faq-one__img {
        position: relative;
        top: 120px;
        left: 0;
    }

    .faq-one__img img {
        width: 100%;
    }

    .faq-one-accrodion .accrodion-title h4::before {
        right: -20px;
    }

    .footer-widget__about {
        margin-right: 0;
    }

    .footer-widget__service {
        margin-top: 50px;
        margin-bottom: 43px;
    }

    .footer-widget__links {
        margin-bottom: 43px;
    }

    .help-people__inner {
        padding: 60px 20px 60px;
    }

    .help-the-causes__left {
        width: 100%;
    }

    .help-the-causes__right {
        width: 100%;
    }

    .help-the-causes__content-box {
        margin-left: 0;
        padding-left: 20px;
        padding-right: 20px;
    }

    .up-comming-event-two__list {
        flex-direction: column;
    }

    .up-comming-event-two__single {
        padding: 30px 10px;
    }

    .help-one__single {
        padding-left: 0;
    }

    .help-one__icon {
        position: relative;
        top: 0;
        margin-bottom: 20px;
    }

    .testimonial-two__single {
        padding: 30px 20px 23px;
        flex-direction: column;
        align-items: baseline;
    }

    .testimonial-two__content {
        margin-left: 0;
        margin-top: 40px;
    }

    .testimonial-two__quote {
        bottom: -20px;
    }

    .blog-three__content {
        padding: 30px 20px 35px;
    }

    .blog-three__title {
        font-size: 22px;
    }

    .cta-one__inner {
        justify-content: center;
        flex-direction: column;
    }

    .cta-one__right {
        margin-top: 27px;
    }

    .cta-one__left {
        flex-direction: column;
        text-align: center;
    }

    .cta-one__title {
        margin-left: 0;
        margin-top: 20px;
    }

    .error-page__img img {
        width: 100%;
    }

    .error-page__text {
        margin-top: 0;
    }

    .error-page {
        padding: 90px 0 120px;
    }

    .blog-details__bottom {
        flex-direction: column;
    }

    .blog-details__tags a {
        font-size: 11px;
        padding: 7px 13px;
    }

    .blog-details__social-list {
        margin-top: 25px;
    }

    .author-one {
        flex-direction: column;
        padding: 60px 30px 60px;
    }

    .author-one__content {
        margin-left: 0;
        margin-top: 17px;
    }

    .comment-one__single {
        flex-direction: column;
    }

    .comment-one__content {
        margin-left: 0;
        margin-top: 25px;
    }

    .sidebar__search {
        padding-left: 20px;
        padding-right: 20px;
    }

    .sidebar__category {
        padding: 35px 20px 40px;
    }

    .sidebar__post {
        padding: 35px 20px 32px;
    }

    .sidebar__support {
        padding: 77px 20px 90px;
    }

    .sidebar__tags {
        padding: 35px 15px 40px;
    }

    .sidebar__comments {
        padding: 45px 20px 43px;
    }

    .contact-page__right {
        margin-top: 50px;
    }

    .contact-info__single {
        padding: 43px 25px 43px;
    }

    .contact-info__single-3 {
        padding: 43px 25px 73px;
    }

    .project-details__right {
        margin-top: 50px;
    }

    .project-details__list li .right {
        margin-left: 15px;
    }

    .join-team__Left {
        margin-bottom: 42px;
    }

    .donations-list__content {
        padding: 30px 30px 23px;
    }

    .donation-details__left {
        margin-bottom: 50px;
    }

    .donation-details__top-progress-box {
        padding: 50px 20px 50px;
    }

    .donation-details__presentation {
        flex-direction: column;
    }

    .donation-details__presentation-icon-box {
        margin-bottom: 25px;
    }

    .donation-details__recent-donation {
        padding-left: 20px;
        padding-right: 20px;
    }

    .donate-now__right {
        margin-top: 50px;
    }

    .donate-now__causes-content {
        padding: 33px 30px 30px;
    }

    .donate-now__progress {
        padding: 53px 30px 29px;
    }

    .donation-details__organizer {
        padding: 50px 30px 41px;
    }

    .site-footer-one__top {
        padding: 115px 0 176px;
    }

    .team-details__top-right {
        margin-left: 0;
        margin-top: 60px;
    }

    .join-team {
        padding: 109px 0 120px;
    }






}


@media only screen and (min-width: 1200px) and (max-width: 1350px) {
    .help-the-causes__content-box {
        margin-left: 20px;
    }

}












/*--------------------------------------------------------------
# Slider All Responsice Css
--------------------------------------------------------------*/

@media only screen and (min-width: 1400px) and (max-width: 1790px) {
    .main-slider-two__img {
        right: 150px;
    }

    .main-slider__nav-two .swiper-button-prev {
        margin-left: 30px;
    }

    .main-slider__nav-two .swiper-button-next {
        margin-right: 30px;
    }

}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .main-slider-two__img {
        right: 115px;
    }

    .main-slider-two__img img {
        width: 80%;
    }

    .main-slider-two__img:before {
        right: 87px;
    }

    .main-slider__nav-two {
        justify-content: inherit;
        flex-wrap: inherit;
        align-items: flex-end;
        flex-direction: column;
        width: 100%;
        max-width: 1200px;
        padding-left: 15px;
        padding-right: 15px;
        position: absolute;
        top: 55%;
        left: 50%;
        z-index: 100;
        transform: translateY(-50%) translateX(-50%);
        height: 0;
    }


    .main-slider__nav-two .swiper-button-next,
    .main-slider__nav-two .swiper-button-prev {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
        margin-left: 0;
        margin-right: 0;
        padding: 13px 0;
    }

    .main-slider__nav-two .swiper-button-prev {
        margin-bottom: 32px;
    }

















}


@media only screen and (min-width: 992px) and (max-width: 1199px) {
    #main-slider-pagination {
        max-width: 960px;
        padding: 0 15px;
    }

    .main-slider-shape-5 {
        display: none;
    }

    .main-slider-arrow {
        right: 630px;
    }

    .main-slider-two__img {
        display: none;
    }

    .main-slider__nav-two {
        justify-content: inherit;
        flex-wrap: inherit;
        align-items: flex-end;
        flex-direction: column;
        width: 100%;
        max-width: 1200px;
        padding-left: 15px;
        padding-right: 15px;
        position: absolute;
        top: 51%;
        left: 50%;
        z-index: 100;
        transform: translateY(-50%) translateX(-50%);
        height: 0;
    }


    .main-slider__nav-two .swiper-button-next,
    .main-slider__nav-two .swiper-button-prev {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
        margin-left: 0;
        margin-right: 0;
        padding: 13px 0;
    }

    .main-slider__nav-two .swiper-button-prev {
        margin-bottom: 32px;
    }






}






@media only screen and (min-width: 768px) and (max-width: 991px) {
    #main-slider-pagination {
        max-width: 720px;
        padding: 0 15px;
    }

    .main-slider-shape-5 {
        display: none;
    }

    .main-slider-two__img {
        display: none;
    }

    .main-slider__nav-two {
        justify-content: inherit;
        flex-wrap: inherit;
        align-items: flex-end;
        flex-direction: column;
        width: 100%;
        max-width: 1200px;
        padding-left: 15px;
        padding-right: 15px;
        position: absolute;
        top: 51%;
        left: 50%;
        z-index: 100;
        transform: translateY(-50%) translateX(-50%);
        height: 0;
    }


    .main-slider__nav-two .swiper-button-next,
    .main-slider__nav-two .swiper-button-prev {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
        margin-left: 0;
        margin-right: 0;
        padding: 13px 0;
    }

    .main-slider__nav-two .swiper-button-prev {
        margin-bottom: 32px;
    }

    .main-slider__nav-three .swiper-button-next {
        margin-right: 60px;
    }

    .main-slider__nav-three .swiper-button-prev {
        margin-left: 60px;
    }






}



@media only screen and (max-width: 767px) {
    #main-slider-pagination {
        max-width: 540px;
        padding: 0 15px;
    }

    .main-slider-shape-5 {
        display: none;
    }

    .main-slider__nav {
        display: none;
    }

    .main-slider h2 br {
        display: none;
    }

    .main-slider h2 {
        font-size: 36px;
        line-height: 46px;
    }

    .main-slider h2 span:before {
        display: none;
    }

    .main-slider p br {
        display: none;
    }

    .main-slider-arrow {
        display: none;
    }

    .main-slider .container {
        padding-top: 140px;
    }

    .main-slider-two__img {
        display: none;
    }

    .main-slider__nav-two {
        display: none;
    }

    .main-slider-two .container {
        padding-top: 267px;
        padding-bottom: 223px;
    }

    .main-slider__nav-three {
        display: none;
    }

    .main-slider-three .container {
        padding-top: 155px;
        padding-bottom: 200px;
    }






}







/*--------------------------------------------------------------
# Main Menu All Responsice Css
--------------------------------------------------------------*/

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .main-menu-wrapper__right {
        padding: 26px 0;
    }


}


@media only screen and (min-width: 768px) and (max-width: 991px) {
    .main-header__top {
        display: none;
    }

    .main-menu-wrapper__right {
        padding: 26px 0;
    }








}




@media (max-width: 767px) {
    .main-header__top {
        display: none;
    }

    .main-header__btn {
        display: none;
    }

    .main-menu-wrapper__main-menu {
        margin-right: 0;
    }

    .main-menu-wrapper__right {
        padding: 42px 0;
    }









}