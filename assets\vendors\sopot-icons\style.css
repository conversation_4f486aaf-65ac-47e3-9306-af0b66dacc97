@font-face {
  font-family: 'icomoon';
  src: url('fonts/icomoon.eot?orkqwr');
  src: url('fonts/icomoon.eot?orkqwr#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?orkqwr') format('truetype'),
    url('fonts/icomoon.woff?orkqwr') format('woff'),
    url('fonts/icomoon.svg?orkqwr#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}







.icon-call:before {
  content: "\e900";
}
.icon-envelope:before {
  content: "\e901";
}
.icon-place:before {
  content: "\e902";
}
.icon-search-interface-symbol:before {
  content: "\e903";
}
.icon-plus:before {
  content: "\e904";
}
.icon-minus:before {
  content: "\e905";
}
.icon-heart:before {
  content: "\e906";
}
.icon-help:before {
  content: "\e907";
}
.icon-donation:before {
  content: "\e908";
}
.icon-bankruptcy:before {
  content: "\e909";
}
.icon-comment:before {
  content: "\e90a";
}
.icon-play-button:before {
  content: "\e90b";
}
.icon-play-button-1:before {
  content: "\e90c";
}
.icon-calendar:before {
  content: "\e90d";
}
.icon-back-in-time:before {
  content: "\e90e";
}
.icon-play:before {
  content: "\e90f";
}
.icon-quote:before {
  content: "\e910";
}
.icon-down:before {
  content: "\e911";
}
.icon-down-arrow:before {
  content: "\e912";
}
.icon-success:before {
  content: "\e913";
}
.icon-error-404:before {
  content: "\e914";
}
.icon-error-message:before {
  content: "\e915";
}
.icon-donation-1:before {
  content: "\e916";
}
.icon-donation-2:before {
  content: "\e917";
}
.icon-online-donation:before {
  content: "\e918";
}
.icon-donation-3:before {
  content: "\e919";
}
.icon-facebook:before {
  content: "\e91a";
}
.icon-twitter:before {
  content: "\e91b";
}
.icon-google-plus:before {
  content: "\e91c";
}
.icon-linkedin:before {
  content: "\e91d";
}
